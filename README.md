# Azure Landing Zone with Terraform

This repository contains Terraform configurations for deploying Azure Landing Zone based on Microsoft's Cloud Adoption Framework (CAF).

## 🏗️ Architecture Overview

The solution implements a hub-and-spoke network topology with the following components:

- **Core Module**: Management groups, policies, and governance framework
- **Management Module**: Log Analytics workspace, monitoring, and management resources
- **Connectivity Module**: Hub network, Azure Firewall, and connectivity resources
- **Spoke Module**: Spoke networks for workloads and applications

## 📋 Prerequisites

- **Azure CLI** installed and configured (`az --version`)
- **Terraform** >= 1.0 (`terraform --version`)
- **Azure Permissions**: Owner or User Access Administrator role at tenant/management group level
- **PowerShell** (recommended for Windows users to avoid path issues)

## 🚀 Quick Start

1. **Clone this repository**
   ```bash
   git clone <repository-url>
   cd ewh-landingzone
   ```

2. **Configure variables**
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   # Edit terraform.tfvars with your values
   ```

3. **Initialize and deploy**
   ```bash
   terraform init
   terraform plan
   terraform apply
   ```

## ⚙️ Configuration

### Required Variables

Update the `terraform.tfvars` file with your specific values:

```hcl
# Organization Configuration
root_id              = "ewhpoc"           # Your organization prefix
root_name            = "EWH POC"          # Display name for root management group
primary_location     = "southeastasia"    # Primary Azure region
secondary_location   = "eastasia"         # Secondary Azure region

# Subscription IDs (must be valid Azure subscription IDs)
subscription_id_connectivity = "bdd510c9-0620-44c9-bade-927705a70d18"
subscription_id_management   = "bdd510c9-0620-44c9-bade-927705a70d18"
subscription_id_identity     = "bdd510c9-0620-44c9-bade-927705a70d18"

# Feature Configuration
enable_ddos_protection = false            # Enable DDoS Protection Standard
```

### Optional Variables

```hcl
# Resource Tags
tags = {
  Environment = "Production"
  Owner       = "Platform Team"
  Project     = "Azure Landing Zone"
}

# Connectivity Tags
connectivity_resources_tags = {
  Purpose = "Hub Network"
  Tier    = "Infrastructure"
}
```

## 📚 Module Documentation

Each module has detailed documentation with configuration options:

| Module | Purpose | Documentation |
|--------|---------|---------------|
| [Core](./core/README.md) | Management groups, policies, and governance | [📖 Core Module Guide](./core/README.md) |
| [Management](./management/README.md) | Log Analytics, monitoring, and management | [📖 Management Module Guide](./management/README.md) |
| [Connectivity](./connectivity/README.md) | Hub network, firewall, and connectivity | [📖 Connectivity Module Guide](./connectivity/README.md) |
| [Spoke](./spoke/README.md) | Spoke networks for workloads | [📖 Spoke Module Guide](./spoke/README.md) |

## 🔄 Deployment Order

The modules are deployed automatically in the correct order due to dependencies:

```mermaid
graph TD
    A[Core Module] --> B[Management Module]
    A --> C[Connectivity Module]
    B --> C
    C --> D[Spoke Module]
```

1. **Core Module** - Creates management groups and policies
2. **Management Module** - Creates resource group and Log Analytics workspace
3. **Connectivity Module** - Uses management resource group for hub network
4. **Spoke Module** - Creates spoke networks (optional)

## 🛠️ Advanced Configuration

### Custom Policy Definitions

Add custom policies in `core/lib/policy_definitions/`:

```json
{
  "name": "Custom-Policy-Name",
  "type": "Microsoft.Authorization/policyDefinitions",
  "properties": {
    "displayName": "Custom Policy Display Name",
    "policyType": "Custom",
    "mode": "All",
    "description": "Custom policy description",
    "policyRule": {
      // Policy rule definition
    }
  }
}
```

### Custom Archetype Definitions

Override default archetypes in `core/lib/archetype_definitions/`:

```json
{
  "custom_archetype": {
    "policy_assignments": ["Policy-Assignment-Name"],
    "policy_definitions": ["Custom-Policy-Name"],
    "policy_set_definitions": [],
    "role_definitions": [],
    "archetype_config": {
      "parameters": {},
      "access_control": {}
    }
  }
}
```

## 🔧 Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| **Permission Errors** | Ensure Owner/User Access Administrator role at management group level |
| **Resource Already Exists** | Check for existing resources from previous deployments |
| **Policy Assignment Failures** | Verify policy definitions exist and are valid |
| **Subnet Not Found** | Check network configuration and dependencies |
| **Git Bash Path Issues** | Use PowerShell instead of Git Bash on Windows |

### Useful Commands

```bash
# Check current Azure context
az account show

# List management groups
az account management-group list --output table

# Check resource groups
az group list --output table

# Refresh Terraform state
terraform refresh

# Target specific module
terraform apply -target=module.core

# Import existing resources
terraform import 'resource.name' '/subscriptions/.../resourceId'

# Check Terraform state
terraform state list
terraform state show 'resource.name'
```

### Debug Mode

Enable detailed logging:

```bash
export TF_LOG=DEBUG
terraform apply
```

## 📊 Monitoring and Maintenance

### Health Checks

After deployment, verify:

- [ ] Management groups created correctly
- [ ] Policy assignments applied
- [ ] Log Analytics workspace operational
- [ ] Hub network connectivity
- [ ] Azure Firewall rules configured
- [ ] Spoke network peering established

### Regular Maintenance

- Review and update policy assignments quarterly
- Monitor Log Analytics workspace usage
- Update Terraform modules to latest versions
- Review and rotate service principal credentials

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Test thoroughly in a development environment
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Guidelines

- Follow Terraform best practices
- Add documentation for new features
- Test changes in isolated environment
- Update version constraints appropriately

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:

- Create an issue in this repository
- Contact the Platform Engineering team
- Review the [troubleshooting guide](#-troubleshooting)

---

**Note**: This Landing Zone implementation follows Microsoft's Cloud Adoption Framework and Enterprise-Scale architecture patterns.
