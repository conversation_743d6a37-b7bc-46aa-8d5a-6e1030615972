# Hub-and-Spoke Network Architecture with Azure Firewall and Bastion

## Overview

This configuration implements a secure hub-and-spoke network architecture in Azure with the following components:

- **Hub Network**: Central connectivity hub with Azure Firewall and Azure Bastion
- **Spoke Networks**: Isolated workload networks (Production, Non-Production, Management)
- **Azure Firewall**: Centralized network security and traffic inspection
- **Azure Bastion**: Secure RDP/SSH access to VMs without public IPs
- **Route Tables**: Force traffic through Azure Firewall for security inspection

## Network Architecture

### Address Space Allocation

| Network Type | Address Space | Purpose |
|--------------|---------------|---------|
| Hub Network | **********/20 | Central connectivity hub |
| Management | ***********/21 | Management and shared services |
| Production | **********/16 | Production workloads |
| Non-Production | **********/16 | Development, testing, UAT |

### Hub Network Subnets

| Subnet | Address Space | Purpose |
|--------|---------------|---------|
| AzureFirewallSubnet | **********/26 | Azure Firewall |
| AzureFirewallSubnet-Reverse | ***********/26 | Azure Firewall (reverse) |
| GatewaySubnet | ************/27 | VPN/ExpressRoute Gateway (future) |
| GatewaySubnet-Reverse | ************/27 | Gateway (reverse) |
| ERGatewaySubnet | ************/27 | ExpressRoute Gateway |
| ERGatewaySubnet-Reverse | ************/27 | ExpressRoute Gateway (reverse) |
| dns-resolver-inbound | **********/27 | DNS resolver inbound |
| dns-resolver-outbound | ***********/27 | DNS resolver outbound |
| AzureBastionSubnet | ***********/26 | Azure Bastion |

## Security Configuration

### Azure Firewall Rules

#### Network Rules
- **Production-to-Production**: Allow internal communication within production networks
- **Non-Production-to-Non-Production**: Allow internal communication within non-production networks
- **Management Access**: Allow management network to access all spokes
- **DNS and NTP**: Allow essential infrastructure services

#### Application Rules
- **Windows Updates**: Allow access to Microsoft update services
- **Azure Services**: Allow access to Azure management endpoints
- **Package Managers**: Allow access to common package repositories

### Route Tables

All spoke networks are configured with User Defined Routes (UDRs) that force traffic through Azure Firewall:

- **Internet Traffic**: 0.0.0.0/0 → Azure Firewall (**********)
- **Inter-Spoke Traffic**: Spoke networks → Azure Firewall
- **Management Access**: Management network → Azure Firewall

### Azure Bastion Configuration

- **SKU**: Standard (supports advanced features)
- **Features Enabled**:
  - Copy/Paste
  - File Copy
  - IP Connect
  - Tunneling
- **Scale Units**: 2 (supports up to 50 concurrent sessions)

## Files Structure

```
connectivity/
├── main.tf                    # Main connectivity resources
├── settings.connectivity.tf   # ALZ connectivity configuration
├── firewall-rules.tf         # Azure Firewall policies and rules
├── bastion.tf                # Azure Bastion configuration
├── output.tf                 # Output values
├── variables.tf              # Variable definitions
└── README.md                 # This documentation
```

## Deployment Order

1. **ALZ Connectivity Module**: Creates hub network, Azure Firewall, and basic infrastructure
2. **Route Tables**: Creates UDRs for traffic routing through firewall
3. **Firewall Policy**: Creates and applies security rules
4. **Azure Bastion**: Creates secure access solution
5. **Spoke Networks**: Creates and peers spoke networks with hub

## Usage

### Accessing VMs via Azure Bastion

1. Navigate to Azure Portal
2. Go to the target VM
3. Click "Connect" → "Bastion"
4. Enter credentials
5. Connect securely without public IP

### Firewall Rule Management

Firewall rules are defined in `firewall-rules.tf` and organized into:
- Network Rule Collection Groups (Layer 3/4 rules)
- Application Rule Collection Groups (Layer 7 rules)
- DNAT Rule Collection Groups (Inbound NAT rules)

### Adding New Spoke Networks

1. Define network configuration in `settings.connectivity.tf`
2. Create spoke module in main Terraform configuration
3. Update firewall rules if needed
4. Apply Terraform configuration

## Monitoring and Troubleshooting

### Azure Firewall Logs

Enable diagnostic settings to send logs to:
- Log Analytics Workspace
- Storage Account
- Event Hub

### Key Metrics to Monitor

- **Firewall Health**: CPU, memory, throughput
- **Rule Hits**: Which rules are being triggered
- **Blocked Traffic**: Denied connections
- **Bastion Sessions**: Active connections and usage

### Common Issues

1. **Connectivity Issues**: Check route tables and firewall rules
2. **Bastion Access**: Verify NSG rules and subnet configuration
3. **DNS Resolution**: Ensure firewall DNS proxy is enabled
4. **Performance**: Monitor firewall throughput and scale units

## Security Best Practices

1. **Principle of Least Privilege**: Only allow necessary traffic
2. **Regular Rule Review**: Audit and update firewall rules
3. **Monitoring**: Enable comprehensive logging and alerting
4. **Network Segmentation**: Use separate subnets for different tiers
5. **Bastion Access**: Use Azure Bastion instead of public IPs for VM access
