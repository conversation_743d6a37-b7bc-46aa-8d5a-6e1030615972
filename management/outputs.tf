# ==============================================================================
# MANAGEMENT MODULE OUTPUTS
# ==============================================================================
# Outputs for the simplified management module with two Log Analytics Workspaces

# ==============================================================================
# RESOURCE GROUP OUTPUTS
# ==============================================================================

output "resource_group_id" {
  description = "ID of the management resource group"
  value       = azurerm_resource_group.management.id
}

output "resource_group_name" {
  description = "Name of the management resource group"
  value       = azurerm_resource_group.management.name
}

output "resource_group_location" {
  description = "Location of the management resource group"
  value       = azurerm_resource_group.management.location
}

# ==============================================================================
# INFRASTRUCTURE LOG ANALYTICS WORKSPACE OUTPUTS
# ==============================================================================

output "infrastructure_law_id" {
  description = "ID of the Infrastructure Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.infrastructure.id
}

output "infrastructure_law_name" {
  description = "Name of the Infrastructure Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.infrastructure.name
}

output "infrastructure_law_workspace_id" {
  description = "Customer ID (workspace ID) for the Infrastructure Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.infrastructure.workspace_id
}

output "infrastructure_law_primary_shared_key" {
  description = "Primary shared key for the Infrastructure Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.infrastructure.primary_shared_key
  sensitive   = true
}

output "infrastructure_law_secondary_shared_key" {
  description = "Secondary shared key for the Infrastructure Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.infrastructure.secondary_shared_key
  sensitive   = true
}

# ==============================================================================
# SECURITY LOG ANALYTICS WORKSPACE OUTPUTS
# ==============================================================================

output "security_law_id" {
  description = "ID of the Security Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.security.id
}

output "security_law_name" {
  description = "Name of the Security Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.security.name
}

output "security_law_workspace_id" {
  description = "Customer ID (workspace ID) for the Security Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.security.workspace_id
}

output "security_law_primary_shared_key" {
  description = "Primary shared key for the Security Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.security.primary_shared_key
  sensitive   = true
}

output "security_law_secondary_shared_key" {
  description = "Secondary shared key for the Security Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.security.secondary_shared_key
  sensitive   = true
}

# ==============================================================================
# MICROSOFT SENTINEL OUTPUTS
# ==============================================================================

output "sentinel_id" {
  description = "ID of Microsoft Sentinel onboarding on Security LAW"
  value       = var.enable_sentinel ? azurerm_sentinel_log_analytics_workspace_onboarding.security[0].id : null
}

output "sentinel_workspace_id" {
  description = "Workspace ID where Microsoft Sentinel is enabled"
  value       = var.enable_sentinel ? azurerm_log_analytics_workspace.security.id : null
}

# ==============================================================================
# DATA COLLECTION RULES OUTPUTS
# ==============================================================================

output "vm_insights_dcr_id" {
  description = "ID of the VM Insights Data Collection Rule for Infrastructure LAW"
  value       = var.enable_vminsights_dcr ? azurerm_monitor_data_collection_rule.vm_insights_infra[0].id : null
}

output "vm_insights_dcr_name" {
  description = "Name of the VM Insights Data Collection Rule for Infrastructure LAW"
  value       = var.enable_vminsights_dcr ? azurerm_monitor_data_collection_rule.vm_insights_infra[0].name : null
}

# ==============================================================================
# SOLUTION OUTPUTS
# ==============================================================================

output "infrastructure_solutions" {
  description = "List of solutions deployed to Infrastructure LAW"
  value = {
    vm_insights         = azurerm_log_analytics_solution.vm_insights_infra.id
    change_tracking     = azurerm_log_analytics_solution.change_tracking_infra.id
    updates            = azurerm_log_analytics_solution.updates_infra.id
    agent_health       = azurerm_log_analytics_solution.agent_health_infra.id
  }
}

output "security_solutions" {
  description = "List of solutions deployed to Security LAW"
  value = {
    security           = azurerm_log_analytics_solution.security.id
    security_insights  = var.enable_sentinel ? azurerm_log_analytics_solution.security_insights[0].id : null
  }
}

# ==============================================================================
# LEGACY COMPATIBILITY OUTPUTS
# ==============================================================================
# These outputs maintain compatibility with existing code that expects single LAW

output "log_analytics_workspace_id" {
  description = "ID of the Infrastructure Log Analytics workspace (legacy compatibility)"
  value       = azurerm_log_analytics_workspace.infrastructure.id
}

output "log_analytics_workspace_name" {
  description = "Name of the Infrastructure Log Analytics workspace (legacy compatibility)"
  value       = azurerm_log_analytics_workspace.infrastructure.name
}

output "log_analytics_workspace_customer_id" {
  description = "Customer ID for the Infrastructure Log Analytics workspace (legacy compatibility)"
  value       = azurerm_log_analytics_workspace.infrastructure.workspace_id
}
