# Management Module - Dual Log Analytics Workspace Architecture

## 🎯 Tổng Quan

Management module đã được thiết kế lại để triển khai **hai Log Analytics Workspace riêng biệt** theo best practices của Microsoft, phân tách rõ ràng giữa infrastructure monitoring và security monitoring.

## 🏗️ Kiến Trúc Dual LAW

### **1. Infrastructure Log Analytics Workspace**
- **Tên**: `{root_id}-law-infrastructure-{location}`
- **Mục đích**: Thu thập log và metrics từ các tài nguyên hạ tầng
- **Retention**: 90 ngày (có thể cấu hình)
- **SKU**: PerGB2018

**Data Sources:**
- Azure VNets, Gateways, Azure Firewall
- Key Vaults, Storage Accounts
- VMs quản lý và hạ tầng
- Log hệ điều hành từ các VM hạ tầng
- Performance counters và metrics

**Solutions:**
- VM Insights
- Change Tracking
- Update Management
- Agent Health Assessment

### **2. Security Log Analytics Workspace**
- **Tên**: `{root_id}-law-security-{location}`
- **<PERSON><PERSON><PERSON> đ<PERSON>**: Thu thập log bảo mật và làm nguồn dữ liệu cho Azure Sentinel
- **Retention**: 180 ngày (có thể cấu hình, khuyến nghị cao hơn cho compliance)
- **SKU**: PerGB2018

**Data Sources:**
- Microsoft Entra ID Sign-in/Audit Logs
- NSG Flow Logs
- Azure Firewall logs
- Microsoft Defender for Cloud alerts
- Azure Activity Logs
- Security events và audit logs

**Solutions:**
- Security
- SecurityInsights (Microsoft Sentinel)

## 📋 Cấu Hình Variables

### **Infrastructure LAW**
```hcl
# Infrastructure Log Analytics Workspace
log_retention_in_days = 90          # Retention cho infrastructure logs
law_sku              = "PerGB2018"  # SKU cho Infrastructure LAW
law_daily_quota_gb   = -1           # Daily quota (-1 = unlimited)
```

### **Security LAW**
```hcl
# Security Log Analytics Workspace  
security_log_retention_in_days = 180  # Retention cho security logs (cao hơn cho compliance)
security_law_daily_quota_gb    = -1   # Daily quota (-1 = unlimited)
```

### **Microsoft Sentinel**
```hcl
enable_sentinel = true  # Enable Sentinel trên Security LAW
```

### **Data Collection Rules**
```hcl
enable_vminsights_dcr      = true  # VM Insights DCR cho Infrastructure LAW
enable_change_tracking_dcr = true  # Change Tracking DCR
```

## 🔧 Resources Được Tạo

### **Resource Group**
- `{root_id}-rg-management-{location}`

### **Infrastructure LAW Resources**
- Log Analytics Workspace: `{root_id}-law-infrastructure-{location}`
- VM Insights Solution
- Change Tracking Solution
- Updates Solution
- Agent Health Assessment Solution
- VM Insights Data Collection Rule

### **Security LAW Resources**
- Log Analytics Workspace: `{root_id}-law-security-{location}`
- Security Solution
- SecurityInsights Solution (nếu Sentinel enabled)
- Microsoft Sentinel onboarding

## 📊 Outputs

### **Infrastructure LAW Outputs**
```hcl
infrastructure_law_id                    # Resource ID
infrastructure_law_name                  # Workspace name
infrastructure_law_workspace_id          # Customer ID
infrastructure_law_primary_shared_key    # Primary key (sensitive)
```

### **Security LAW Outputs**
```hcl
security_law_id                         # Resource ID
security_law_name                       # Workspace name
security_law_workspace_id               # Customer ID
security_law_primary_shared_key         # Primary key (sensitive)
```

### **Sentinel Outputs**
```hcl
sentinel_id                             # Sentinel onboarding ID
sentinel_workspace_id                   # Workspace where Sentinel is enabled
```

### **Legacy Compatibility**
```hcl
log_analytics_workspace_id              # Points to Infrastructure LAW
log_analytics_workspace_name            # Points to Infrastructure LAW
log_analytics_workspace_customer_id     # Points to Infrastructure LAW
```

## 🔄 Migration từ Single LAW

Nếu bạn đang migrate từ single LAW architecture:

1. **Backup existing data** nếu cần thiết
2. **Update variables** trong terraform.tfvars
3. **Run terraform plan** để xem changes
4. **Apply changes** - sẽ tạo thêm Security LAW
5. **Reconfigure data sources** để point đến đúng workspace

## 💰 Cost Optimization

### **Infrastructure LAW**
- Retention: 90 ngày (balance giữa cost và operational needs)
- Daily quota: Unlimited (monitor và adjust nếu cần)

### **Security LAW**
- Retention: 180 ngày (compliance requirements)
- Daily quota: Unlimited (security data quan trọng)

### **Cost Monitoring**
- Monitor daily ingestion qua Azure Portal
- Set up alerts cho unusual spikes
- Review retention policies định kỳ

## 🔒 Security Best Practices

1. **Access Control**: Phân quyền riêng biệt cho từng workspace
2. **Data Separation**: Infrastructure và security data hoàn toàn tách biệt
3. **Retention Policies**: Security logs giữ lâu hơn cho compliance
4. **Sentinel Integration**: Chỉ security data được feed vào Sentinel

## 🚀 Deployment

```bash
# Deploy management module
terraform plan -target=module.management
terraform apply -target=module.management

# Verify workspaces
az monitor log-analytics workspace list --resource-group {root_id}-rg-management-{location}
```

## 📈 Monitoring và Maintenance

### **Health Checks**
- [ ] Infrastructure LAW receiving VM metrics
- [ ] Security LAW receiving security events
- [ ] Sentinel analytics rules working
- [ ] Data retention policies applied correctly

### **Regular Tasks**
- Monitor ingestion costs monthly
- Review and optimize retention policies
- Update DCR configurations as needed
- Maintain Sentinel analytics rules
