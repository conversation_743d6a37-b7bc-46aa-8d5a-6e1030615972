# Management Module Variables
# Compatible with Azure CAF Enterprise Scale configure_management_resources structure

variable "root_id" {
  type        = string
  description = "Specifies the ID of the Enterprise-scale root Management Group, used as a prefix for resources created by this module."

  validation {
    condition     = can(regex("[a-zA-Z0-9-_\\(\\)\\.]", var.root_id))
    error_message = "Value must consist of alphanumeric characters and hyphens."
  }
}

variable "root_name" {
  type        = string
  description = "Specifies the display name of the Enterprise-scale root Management Group."
  default     = "EWH"
}

variable "subscription_id_management" {
  type        = string
  description = "Specifies the Subscription ID for the Subscription containing all management resources."
  default     = null

  validation {
    condition     = var.subscription_id_management == null || can(regex("^[a-z0-9-]{36}$", var.subscription_id_management))
    error_message = "Value must be a valid Subscription ID (GUID) or null."
  }
}

variable "primary_location" {
  type        = string
  description = "Sets the default location used for resource deployments where needed."
  default     = "eastus"
}

variable "location" {
  type        = string
  description = "Sets the default location used for resource deployments where needed."
  default     = "eastus"
}

variable "tags" {
  type        = map(string)
  description = "If specified, will set the default tags for all resources deployed by this module where supported."
  default     = {}
}

# Management Resources Configuration Variables
variable "deploy_management_resources" {
  type        = bool
  description = "Controls whether to deploy the management resources into the current Subscription context."
  default     = true
}

# ==============================================================================
# LOG ANALYTICS WORKSPACE CONFIGURATION
# ==============================================================================

# Infrastructure LAW Configuration
variable "log_retention_in_days" {
  type        = number
  description = "Data retention in days for Infrastructure Log Analytics workspace"
  default     = 90

  validation {
    condition     = var.log_retention_in_days >= 30 && var.log_retention_in_days <= 730
    error_message = "Log retention must be between 30 and 730 days."
  }
}

variable "law_sku" {
  type        = string
  description = "SKU for Infrastructure Log Analytics workspace"
  default     = "PerGB2018"

  validation {
    condition     = contains(["Free", "Standalone", "PerNode", "PerGB2018"], var.law_sku)
    error_message = "LAW SKU must be one of: Free, Standalone, PerNode, PerGB2018."
  }
}

variable "law_daily_quota_gb" {
  type        = number
  description = "Daily quota in GB for Infrastructure Log Analytics workspace (-1 for unlimited)"
  default     = -1
}

# Security LAW Configuration
variable "security_log_retention_in_days" {
  type        = number
  description = "Data retention in days for Security Log Analytics workspace"
  default     = 180

  validation {
    condition     = var.security_log_retention_in_days >= 30 && var.security_log_retention_in_days <= 730
    error_message = "Security log retention must be between 30 and 730 days."
  }
}

variable "security_law_daily_quota_gb" {
  type        = number
  description = "Daily quota in GB for Security Log Analytics workspace (-1 for unlimited)"
  default     = -1
}

variable "security_alerts_email_address" {
  type        = string
  description = "Email address for security contact"
  default     = "<EMAIL>"
}

variable "management_resources_location" {
  type        = string
  description = "Location for management resources"
  default     = null
}

variable "management_resources_tags" {
  type        = map(string)
  description = "Tags for management resources"
  default     = {}
}

# Advanced Configuration Variables (optional)
variable "enable_ama" {
  type        = bool
  description = "Enable Azure Monitor Agent (AMA) configuration"
  default     = true
}

variable "enable_vminsights_dcr" {
  type        = bool
  description = "Enable VM Insights Data Collection Rule"
  default     = true
}

variable "enable_change_tracking_dcr" {
  type        = bool
  description = "Enable Change Tracking Data Collection Rule"
  default     = true
}

variable "enable_mdfc_defender_for_sql_dcr" {
  type        = bool
  description = "Enable Microsoft Defender for Cloud SQL Data Collection Rule"
  default     = false
}

variable "enable_monitoring_for_vm" {
  type        = bool
  description = "Enable monitoring for virtual machines"
  default     = true
}

variable "enable_monitoring_for_vmss" {
  type        = bool
  description = "Enable monitoring for virtual machine scale sets"
  default     = true
}

variable "enable_sentinel" {
  type        = bool
  description = "Enable Microsoft Sentinel"
  default     = true
}

variable "enable_change_tracking" {
  type        = bool
  description = "Enable Change Tracking solution"
  default     = true
}

# Microsoft Defender for Cloud Configuration
variable "enable_defender_for_apis" {
  type        = bool
  description = "Enable Microsoft Defender for APIs"
  default     = true
}

variable "enable_defender_for_app_services" {
  type        = bool
  description = "Enable Microsoft Defender for App Services"
  default     = true
}

variable "enable_defender_for_arm" {
  type        = bool
  description = "Enable Microsoft Defender for ARM"
  default     = true
}

variable "enable_defender_for_containers" {
  type        = bool
  description = "Enable Microsoft Defender for Containers"
  default     = true
}

variable "enable_defender_for_cosmosdbs" {
  type        = bool
  description = "Enable Microsoft Defender for Cosmos DB"
  default     = true
}

variable "enable_defender_for_cspm" {
  type        = bool
  description = "Enable Microsoft Defender for CSPM"
  default     = true
}

variable "enable_defender_for_dns" {
  type        = bool
  description = "Enable Microsoft Defender for DNS"
  default     = true
}

variable "enable_defender_for_key_vault" {
  type        = bool
  description = "Enable Microsoft Defender for Key Vault"
  default     = true
}

variable "enable_defender_for_oss_databases" {
  type        = bool
  description = "Enable Microsoft Defender for Open Source Databases"
  default     = true
}

variable "enable_defender_for_servers" {
  type        = bool
  description = "Enable Microsoft Defender for Servers"
  default     = true
}

variable "enable_defender_for_servers_vulnerability_assessments" {
  type        = bool
  description = "Enable vulnerability assessments for Microsoft Defender for Servers"
  default     = true
}

variable "enable_defender_for_sql_servers" {
  type        = bool
  description = "Enable Microsoft Defender for SQL Servers"
  default     = true
}

variable "enable_defender_for_sql_server_vms" {
  type        = bool
  description = "Enable Microsoft Defender for SQL Server VMs"
  default     = true
}

variable "enable_defender_for_storage" {
  type        = bool
  description = "Enable Microsoft Defender for Storage"
  default     = true
}




