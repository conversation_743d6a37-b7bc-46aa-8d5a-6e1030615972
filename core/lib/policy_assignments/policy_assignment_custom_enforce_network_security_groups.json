{"name": "Enforce-NSG", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "This policy ensures that all subnets have Network Security Groups attached, except for specific excluded subnets like GatewaySubnet, AzureFirewallSubnet, etc.", "displayName": "Enforce Network Security Groups on subnets", "notScopes": [], "parameters": {}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517", "nonComplianceMessages": [{"message": "Subnets {enforcementMode} have Network Security Groups attached."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}