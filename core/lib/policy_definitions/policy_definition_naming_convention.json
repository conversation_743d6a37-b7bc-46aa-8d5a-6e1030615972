{"name": "naming_convention", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Enforce EWH Landing Zone Naming Convention", "description": "This policy enforces naming conventions for Azure resources according to EWH Landing Zone standards.", "metadata": {"version": "1.0.0", "category": "General"}, "parameters": {}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Compute/virtualMachines"}, {"not": {"anyOf": [{"field": "name", "like": "ewh-vm-*"}, {"field": "name", "like": "abc-vm-*"}, {"field": "name", "like": "xyz-vm-*"}]}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"not": {"anyOf": [{"field": "name", "like": "ewhst*"}, {"field": "name", "like": "abcst*"}, {"field": "name", "like": "xyzst*"}]}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks"}, {"not": {"anyOf": [{"field": "name", "like": "ewh-vnet-*"}, {"field": "name", "like": "abc-vnet-*"}, {"field": "name", "like": "xyz-vnet-*"}]}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups"}, {"not": {"anyOf": [{"field": "name", "like": "ewh-nsg-*"}, {"field": "name", "like": "abc-nsg-*"}, {"field": "name", "like": "xyz-nsg-*"}]}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Resources/resourceGroups"}, {"not": {"anyOf": [{"field": "name", "like": "ewh-rg-*"}, {"field": "name", "like": "abc-rg-*"}, {"field": "name", "like": "xyz-rg-*"}]}}]}]}, "then": {"effect": "deny"}}}}