{"name": "naming_convention_containers", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Containers", "description": "This policy enforces naming conventions for container resources (AKS) according to EWH Landing Zone organizational standards.", "policyType": "Custom", "mode": "All", "metadata": {"version": "1.0.0", "category": "General"}, "parameters": {}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.ContainerService/managedClusters"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'aks'))]", "equals": true}]}]}]}, "then": {"effect": "deny"}}}}