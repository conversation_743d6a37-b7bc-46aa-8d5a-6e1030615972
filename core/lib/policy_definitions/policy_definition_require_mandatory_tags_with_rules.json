{"name": "require_mandatory_tags_with_rules", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "properties": {"displayName": "Require mandatory tags on resources with specific rules", "description": "Enforce mandatory tags with defined formats or allowed values for EWH Landing Zone resources.", "policyType": "Custom", "mode": "Indexed", "metadata": {"category": "Tags", "version": "1.0.0"}, "parameters": {"Owner": {"type": "String", "metadata": {"displayName": "Owner <PERSON>", "description": "Email of responsible owner (must end with valid domain)"}, "defaultValue": "Owner"}, "org": {"type": "String", "metadata": {"displayName": "Org Tag", "description": "Name of the organization"}, "defaultValue": "Organization"}, "created_by": {"type": "String", "metadata": {"displayName": "Created By Tag", "description": "Email of creator"}, "defaultValue": "CreatedBy"}, "operation_team": {"type": "String", "metadata": {"displayName": "Operation Team Tag", "description": "Name of operational team"}, "defaultValue": "OperationTeam"}, "project_name": {"type": "String", "metadata": {"displayName": "Project Name Tag", "description": "Name of the project"}, "defaultValue": "ProjectName"}, "env": {"type": "String", "metadata": {"displayName": "Environment Tag", "description": "Environment name (Production, Development, Testing, UAT)"}, "allowedValues": ["Production", "Development", "Testing", "UAT"], "defaultValue": "Production"}, "app_name": {"type": "String", "metadata": {"displayName": "App Name Tag", "description": "Name of the application"}, "defaultValue": "ApplicationName"}, "resource_type": {"type": "String", "metadata": {"displayName": "Resource Type Tag", "description": "Resource classification (Infrastructure, Application, Data, Security)"}, "allowedValues": ["Infrastructure", "Application", "Data", "Security"], "defaultValue": "Infrastructure"}, "priority": {"type": "String", "metadata": {"displayName": "Priority Tag", "description": "Priority level (High, Medium, Low)"}, "allowedValues": ["High", "Medium", "Low"], "defaultValue": "Medium"}, "data_zone": {"type": "String", "metadata": {"displayName": "Data Zone Tag", "description": "Data zone (Public, Internal, Confidential, Restricted)"}, "allowedValues": ["Public", "Internal", "Confidential", "Restricted"], "defaultValue": "Public"}, "cost_center": {"type": "String", "metadata": {"displayName": "Cost Center Tag", "description": "Cost center code for financial tracking"}, "defaultValue": "CostCenter"}, "managed_by": {"type": "String", "metadata": {"displayName": "Managed By Tag", "description": "Tool or team managing the resource"}, "defaultValue": "ManagedBy"}}, "policyRule": {"if": {"anyOf": [{"field": "[concat('tags[', parameters('Owner'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('org'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('created_by'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('operation_team'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('project_name'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('env'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('app_name'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('resource_type'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('priority'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('data_zone'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('cost_center'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('managed_by'), ']')]", "exists": false}]}, "then": {"effect": "deny"}}}}