# Custom Policy Set Definitions

Th<PERSON> mục này chứa các custom policy set definitions để override các policy set definitions mặc định từ Azure CAF Enterprise Scale module.

## Enforce-Encryption-CMK Policy Set Definition

### Vấn đề
Policy set definition `Enforce-Encryption-CMK` từ Azure CAF Enterprise Scale module có lỗi với parameter `cognitiveSearchCmk`:

- **Lỗi**: Parameter `cognitiveSearchCmk` có default value là `"Deny"` nhưng Azure built-in policy definition `76a56461-9dc0-40f0-82f5-2453283afa2f` (Azure AI Search services should use customer-managed keys to encrypt data at rest) chỉ cho phép:
  - `AuditIfNotExists` (default)
  - `Disabled`

### Giải pháp
Tạo custom policy set definition trong `core/lib/policy_set_definitions/policy_set_definition_es_enforce_encryption_cmk.json` với:

1. **Parameter `cognitiveSearchCmk` được fix**:
   ```json
   "cognitiveSearchCmk": {
     "type": "string",
     "defaultValue": "AuditIfNotExists",
     "allowedValues": [
       "AuditIfNotExists",
       "Disabled"
     ]
   }
   ```

2. **Tất cả policy definitions khác giữ nguyên** để đảm bảo tính nhất quán.

### Cách hoạt động
- Azure CAF Enterprise Scale module sử dụng `library_path = "${path.module}/lib"` trong `core/main.tf`
- Module sẽ ưu tiên sử dụng custom policy set definitions từ thư mục `core/lib` thay vì từ module mặc định
- Khi chạy `terraform init`, custom policy set definition này sẽ được sử dụng thay vì cái từ module

### Lợi ích
1. **Persistent**: Thay đổi không bị mất khi chạy `terraform init`
2. **Version controlled**: Được lưu trong repository
3. **Reusable**: Mọi người clone repository đều có fix này
4. **Maintainable**: Dễ dàng cập nhật khi cần thiết

### Lưu ý
- File này override hoàn toàn policy set definition `Enforce-Encryption-CMK` từ module
- Nếu Azure CAF Enterprise Scale module cập nhật policy set definition này, bạn cần cập nhật file custom này tương ứng
- Kiểm tra Azure Policy documentation để đảm bảo các parameter values phù hợp với Azure built-in policies
