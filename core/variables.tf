# Use variables to customize the deployment

variable "root_id" {
  type    = string
  default = "myorg"
}

variable "root_name" {
  type    = string
  default = "My Organization"
}

variable "primary_location" {
  type        = string
  description = "Sets the location for \"primary\" resources to be created in."
  default     = "southeastasia"
}

variable "subscription_id_identity" {
  type        = string
  description = "Subscription ID to use for \"identity\" resources."
  default     = ""
}

variable "subscription_id_connectivity" {
  type        = string
  description = "Subscription ID to use for \"connectivity\" resources."
  default     = ""
}

variable "subscription_id_management" {
  type        = string
  description = "Subscription ID to use for \"management\" resources."
  default     = ""
}

# Removed configure_connectivity_resources and configure_management_resources
# These are now handled by separate modules

variable "azure_ad_groups" {
  description = "Map of Azure AD groups to create with their configurations"
  type = map(object({
    display_name         = string
    description          = string
    security_enabled     = bool
    assignable_to_role   = bool
    mail_enabled         = optional(bool, false)
    members              = optional(list(string), [])
    additional_owners    = optional(list(string), [])
    azure_roles          = optional(list(string), [])
    directory_roles      = optional(list(string), [])
  }))
  default = {}
}

variable "assign_roles_to_all_subscriptions" {
  type        = bool
  description = "If set to true, will assign Azure roles to all subscriptions in the tenant. If false, only assigns to current subscription."
  default     = false
}

variable "enable_directory_role_assignments" {
  type        = bool
  description = "If set to true, will enable Azure AD directory role assignments. Requires Privileged Role Administrator permissions."
  default     = false
}

variable "log_retention_in_days" {
  type        = number
  description = "Number of days to retain logs in Log Analytics workspace"
  default     = 30
}

variable "enable_sentinel" {
  type        = bool
  description = "Enable Azure Sentinel on the Log Analytics workspace"
  default     = false
}

variable "email_security_contact" {
  type        = string
  description = "Email address for security contact"
  default     = "<EMAIL>"
}