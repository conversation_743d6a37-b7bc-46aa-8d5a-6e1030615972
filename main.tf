# Configure Terraform to set the required AzureRM provider
# version and features{} block

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
    azuread = {
      source  = "hashicorp/azuread"
      version = "~> 2.47"
    }
  }
}

# Define the provider configuration
provider "azurerm" {
  features {}
}

# Add aliased providers to manage existing resources in state
provider "azurerm" {
  alias           = "networking"
  subscription_id = local.subscription_id_connectivity
  features {}
}

provider "azurerm" {
  alias           = "management"
  subscription_id = local.subscription_id_management
  features {}
}

provider "azurerm" {
  alias           = "sharedservices"
  subscription_id = local.subscription_id_management
  features {}
}

# Get the current client configuration from the AzureRM provider
data "azurerm_client_config" "current" {}

# Logic to handle 1-3 platform subscriptions as available
locals {
  subscription_id_connectivity = coalesce(var.subscription_id_connectivity, local.subscription_id_management)
  subscription_id_identity     = coalesce(var.subscription_id_identity, local.subscription_id_management)
  subscription_id_management   = coalesce(var.subscription_id_management, data.azurerm_client_config.current.subscription_id)
}

# ==============================================================================
# MODULE ORCHESTRATION
# The following module declarations act to orchestrate the
# independently defined module instances for core, connectivity, management and spoke resources
# ==============================================================================

# Connectivity module for Hub network infrastructure
# This module depends on management module for resource group
module "connectivity" {
  source = "./connectivity"

  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }

  connectivity_resources_tags  = var.connectivity_resources_tags
  enable_ddos_protection       = var.enable_ddos_protection
  primary_location             = var.primary_location
  root_id                      = var.root_id
  secondary_location           = var.secondary_location
  subscription_id_connectivity = local.subscription_id_connectivity
  subscription_id_management   = local.subscription_id_management
  core_module_dependency       = module.core

  # Ensure management module creates resource group first and core module completes
  depends_on = [module.management, module.core]
}

module "core" {
  source = "./core"

  root_id          = var.root_id
  root_name        = var.root_name
  primary_location = var.primary_location

  # Subscription IDs for mapping to management groups
  subscription_id_identity     = local.subscription_id_identity
  subscription_id_connectivity = local.subscription_id_connectivity
  subscription_id_management   = local.subscription_id_management

  # Azure AD Groups configuration
  azure_ad_groups                   = var.azure_ad_groups
  assign_roles_to_all_subscriptions = var.assign_roles_to_all_subscriptions
  enable_directory_role_assignments = var.enable_directory_role_assignments
}

# Management module for Log Analytics, Security Center, and AMA (Custom Implementation)
# This module depends on core module and provides management infrastructure
module "management" {
  source = "./management"

  root_id                        = var.root_id
  root_name                      = var.root_name
  primary_location               = var.primary_location
  location                       = var.primary_location
  subscription_id_management     = local.subscription_id_management
  security_alerts_email_address = var.email_security_contact
  log_retention_in_days          = var.log_retention_in_days

  # Advanced monitoring configuration
  enable_ama                           = false
  enable_vminsights_dcr                = false
  enable_change_tracking_dcr           = false
  enable_mdfc_defender_for_sql_dcr     = false
  enable_monitoring_for_vm             = false
  enable_monitoring_for_vmss           = false
  enable_sentinel                      = false
  enable_change_tracking               = false

  # Microsoft Defender for Cloud configuration
  enable_defender_for_apis                              = false
  enable_defender_for_app_services                      = false
  enable_defender_for_arm                               = false
  enable_defender_for_containers                        = false
  enable_defender_for_cosmosdbs                         = false
  enable_defender_for_cspm                              = false
  enable_defender_for_dns                               = false
  enable_defender_for_key_vault                         = false
  enable_defender_for_oss_databases                     = false
  enable_defender_for_servers                           = false
  enable_defender_for_servers_vulnerability_assessments = false
  enable_defender_for_sql_servers                       = false
  enable_defender_for_sql_server_vms                    = false
  enable_defender_for_storage                           = false

  # Tags
  tags = var.tags
}

# ==============================================================================
# SPOKE MODULE
# ==============================================================================
# Single spoke module that creates all enabled spoke networks
# Configuration is managed in spoke/settings.spoke.tf

module "spokes" {
  source = "./spoke"
  count  = var.deploy_spokes ? 1 : 0

  providers = {
    azurerm = azurerm
  }

  root_id                     = var.root_id
  location                    = var.primary_location
  spoke_resources_tags        = var.spoke_resources_tags

  # Hub network information from connectivity module
  hub_vnet_id                 = module.connectivity.hub_network.vnet_id
  hub_vnet_name               = module.connectivity.hub_network.vnet_name
  hub_resource_group_name     = module.connectivity.hub_network.resource_group_name
  hub_address_space           = "**********/16"
  management_address_space    = "***********/21"

  # Peering configuration
  use_remote_gateways         = false
  allow_gateway_transit       = true
  enable_route_table_association = false

  # Ensure connectivity module is deployed first
  depends_on = [module.connectivity]
}


