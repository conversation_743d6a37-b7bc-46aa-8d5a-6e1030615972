# Use variables to customize the deployment

variable "root_id" {
  type        = string
  description = "Sets the value used for generating unique resource naming within the module."
  default     = "ewh"
}

variable "root_name" {
  type        = string
  description = "Sets the value used for the \"intermediate root\" management group display name."
  default     = "EWH"
}

variable "primary_location" {
  type        = string
  description = "Sets the location for \"primary\" resources to be created in."
  default     = "southeastasia"
}

variable "secondary_location" {
  type        = string
  description = "Sets the location for \"secondary\" resources to be created in."
  default     = "eastasia"
}

variable "email_security_contact" {
  type        = string
  description = "Set a custom value for the security contact email address."
  default     = "<email_security_contact>"
}

# ==============================================================================
# LOG ANALYTICS WORKSPACE VARIABLES
# ==============================================================================

# Infrastructure LAW Configuration
variable "log_retention_in_days" {
  type        = number
  description = "Set a custom value for how many days to store logs in the Infrastructure Log Analytics workspace."
  default     = 90
}

variable "law_sku" {
  type        = string
  description = "SKU for Infrastructure Log Analytics workspace"
  default     = "PerGB2018"
}

variable "law_daily_quota_gb" {
  type        = number
  description = "Daily quota in GB for Infrastructure Log Analytics workspace (-1 for unlimited)"
  default     = -1
}

# Security LAW Configuration
variable "security_log_retention_in_days" {
  type        = number
  description = "Set a custom value for how many days to store logs in the Security Log Analytics workspace."
  default     = 180
}

variable "security_law_daily_quota_gb" {
  type        = number
  description = "Daily quota in GB for Security Log Analytics workspace (-1 for unlimited)"
  default     = -1
}

# Data Collection Rules
variable "enable_vminsights_dcr" {
  type        = bool
  description = "Enable VM Insights Data Collection Rule for Infrastructure LAW"
  default     = true
}

variable "enable_change_tracking_dcr" {
  type        = bool
  description = "Enable Change Tracking Data Collection Rule"
  default     = true
}

# Microsoft Sentinel
variable "enable_sentinel" {
  type        = bool
  description = "Enable Microsoft Sentinel on Security Log Analytics workspace"
  default     = true
}

variable "enable_ddos_protection" {
  type        = bool
  description = "Controls whether to create a DDoS Network Protection plan and link to hub virtual networks."
  default     = false
}

variable "subscription_id_connectivity" {
  type        = string
  description = "Subscription ID to use for \"connectivity\" resources."
  default     = ""
}

variable "subscription_id_identity" {
  type        = string
  description = "Subscription ID to use for \"identity\" resources."
  default     = ""
}

variable "subscription_id_management" {
  type        = string
  description = "Subscription ID to use for \"management\" resources."
  default     = ""
}

variable "azure_ad_groups" {
  description = "Map of Azure AD groups to create with their configurations"
  type = map(object({
    display_name         = string
    description          = string
    security_enabled     = bool
    assignable_to_role   = bool
    mail_enabled         = optional(bool, false)
    members              = optional(list(string), [])
    additional_owners    = optional(list(string), [])
    azure_roles          = optional(list(string), [])
    directory_roles      = optional(list(string), [])
  }))
  default = {}
}

variable "assign_roles_to_all_subscriptions" {
  type        = bool
  description = "If set to true, will assign Azure roles to all subscriptions in the tenant. If false, only assigns to current subscription."
  default     = false
}

variable "enable_directory_role_assignments" {
  type        = bool
  description = "If set to true, will enable Azure AD directory role assignments. Requires Privileged Role Administrator permissions."
  default     = false
}

variable "connectivity_resources_tags" {
  type        = map(string)
  description = "Additional tags to apply specifically to connectivity resources"
  default = {
    Purpose = "Networking"
    Component = "Connectivity"
  }
}

variable "management_resources_tags" {
  type        = map(string)
  description = "Additional tags to apply specifically to management resources"
  default = {
    deployedBy = "CTS"
    type       = "management-resources"
  }
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all resources created by this module. MUST include all mandatory tags: Owner, Organization, CreatedBy, OperationTeam, ProjectName, Environment, ApplicationName, ResourceType, Priority, DataZone, CostCenter, and ManagedBy."
  default = {
    # Mandatory Tags (Required by Policy)
    Owner           = "<EMAIL>"
    Organization    = "EWH"
    CreatedBy       = "<EMAIL>"
    OperationTeam   = "Platform Team"
    ProjectName     = "EWH Landing Zone"
    Environment     = "Production"
    ApplicationName = "Platform Infrastructure"
    ResourceType    = "Infrastructure"
    Priority        = "High"
    DataZone        = "Internal"
    CostCenter      = "IT-001"
    ManagedBy       = "Terraform"
    
    # Additional Tags (Optional but Recommended)
    BusinessUnit    = "IT"
    Compliance      = "ISO27001"
    BackupRequired  = "Yes"
    Monitoring      = "Yes"
    DRPlan          = "Yes"
  }
}

variable "spoke_resources_tags" {
  type        = map(string)
  description = "Specify tags to add to \"spoke\" resources."
  default = {
    deployedBy = "CTS"
    type       = "spoke-resources"
  }
}

variable "deploy_spokes" {
  type        = bool
  description = "Controls whether to deploy spoke networks. Individual spokes are controlled by 'enabled' flag in settings.spoke.tf"
  default     = false
}


